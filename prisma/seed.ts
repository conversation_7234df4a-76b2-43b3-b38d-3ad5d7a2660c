import { PrismaClient } from '@prisma/client';
import { auth } from '../lib/auth';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create default roles
  console.log('📝 Creating default roles...');
  
  const adminRole = await prisma.role.upsert({
    where: { name: 'admin' },
    update: {},
    create: {
      name: 'admin',
    },
  });

  const userRole = await prisma.role.upsert({
    where: { name: 'user' },
    update: {},
    create: {
      name: 'user',
    },
  });

  const managerRole = await prisma.role.upsert({
    where: { name: 'manager' },
    update: {},
    create: {
      name: 'manager',
    },
  });

  console.log('✅ Roles created:', { adminRole, userRole, managerRole });

  // Create default admin user
  console.log('👤 Creating default admin user...');
  
  const adminEmail = '<EMAIL>';
  const adminPassword = 'admin123'; // Change this in production!

  // Check if admin user already exists
  const existingAdmin = await prisma.user.findUnique({
    where: { email: adminEmail },
  });

  if (!existingAdmin) {
    // Use better-auth to create the admin user with proper password hashing
    const ctx = await auth.$context;
    
    try {
      const adminUser = await ctx.adapter.create({
        model: 'user',
        data: {
          email: adminEmail,
          name: 'System Administrator',
          emailVerified: true,
          role: 'admin',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      // Create credential account for the admin user
      await ctx.adapter.create({
        model: 'account',
        data: {
          userId: adminUser.id,
          providerId: 'credential',
          accountId: adminUser.id,
          password: await ctx.password.hash(adminPassword),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      // Assign admin role to the user
      await prisma.userRole.create({
        data: {
          userId: adminUser.id,
          roleId: adminRole.id,
        },
      });

      console.log('✅ Admin user created:', {
        id: adminUser.id,
        email: adminUser.email,
        name: adminUser.name,
        role: adminUser.role,
      });
    } catch (error) {
      console.error('❌ Error creating admin user:', error);
    }
  } else {
    console.log('ℹ️ Admin user already exists:', {
      id: existingAdmin.id,
      email: existingAdmin.email,
      name: existingAdmin.name,
    });
  }

  // Create default regular user
  console.log('👤 Creating default regular user...');
  
  const userEmail = '<EMAIL>';
  const userPassword = 'user123'; // Change this in production!

  const existingUser = await prisma.user.findUnique({
    where: { email: userEmail },
  });

  if (!existingUser) {
    const ctx = await auth.$context;
    
    try {
      const regularUser = await ctx.adapter.create({
        model: 'user',
        data: {
          email: userEmail,
          name: 'Regular User',
          emailVerified: true,
          role: 'user',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      // Create credential account for the regular user
      await ctx.adapter.create({
        model: 'account',
        data: {
          userId: regularUser.id,
          providerId: 'credential',
          accountId: regularUser.id,
          password: await ctx.password.hash(userPassword),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      // Assign user role to the user
      await prisma.userRole.create({
        data: {
          userId: regularUser.id,
          roleId: userRole.id,
        },
      });

      console.log('✅ Regular user created:', {
        id: regularUser.id,
        email: regularUser.email,
        name: regularUser.name,
        role: regularUser.role,
      });
    } catch (error) {
      console.error('❌ Error creating regular user:', error);
    }
  } else {
    console.log('ℹ️ Regular user already exists:', {
      id: existingUser.id,
      email: existingUser.email,
      name: existingUser.name,
    });
  }

  // Create default manager user
  console.log('👤 Creating default manager user...');
  
  const managerEmail = '<EMAIL>';
  const managerPassword = 'manager123'; // Change this in production!

  const existingManager = await prisma.user.findUnique({
    where: { email: managerEmail },
  });

  if (!existingManager) {
    const ctx = await auth.$context;
    
    try {
      const managerUser = await ctx.adapter.create({
        model: 'user',
        data: {
          email: managerEmail,
          name: 'Service Manager',
          emailVerified: true,
          role: 'manager',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      // Create credential account for the manager user
      await ctx.adapter.create({
        model: 'account',
        data: {
          userId: managerUser.id,
          providerId: 'credential',
          accountId: managerUser.id,
          password: await ctx.password.hash(managerPassword),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      // Assign manager role to the user
      await prisma.userRole.create({
        data: {
          userId: managerUser.id,
          roleId: managerRole.id,
        },
      });

      console.log('✅ Manager user created:', {
        id: managerUser.id,
        email: managerUser.email,
        name: managerUser.name,
        role: managerUser.role,
      });
    } catch (error) {
      console.error('❌ Error creating manager user:', error);
    }
  } else {
    console.log('ℹ️ Manager user already exists:', {
      id: existingManager.id,
      email: existingManager.email,
      name: existingManager.name,
    });
  }

  console.log('🎉 Database seeding completed!');
  console.log('\n📋 Default credentials:');
  console.log('Admin: <EMAIL> / admin123');
  console.log('Manager: <EMAIL> / manager123');
  console.log('User: <EMAIL> / user123');
  console.log('\n⚠️  Please change these passwords in production!');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
