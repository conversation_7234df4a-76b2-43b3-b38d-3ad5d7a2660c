# Better Auth Implementation Guide

This project has been configured with Better Auth for authentication and authorization. Here's everything you need to know about the setup.

## 🚀 Features Implemented

- ✅ Better Auth with Prisma adapter
- ✅ Email/password authentication
- ✅ Role-based access control (RBAC)
- ✅ Database seeding with default users and roles
- ✅ PostgreSQL database integration
- ✅ Admin plugin for user management

## 📋 Default User Accounts

The database has been seeded with the following default accounts:

### Admin User
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Role**: `admin`
- **Permissions**: Full system access, user management

### Manager User
- **Email**: `<EMAIL>`
- **Password**: `manager123`
- **Role**: `manager`
- **Permissions**: Service management, limited admin access

### Regular User
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Role**: `user`
- **Permissions**: Basic user access

> ⚠️ **Security Warning**: Please change these default passwords in production!

## 🛠 Available Scripts

### Database Management
```bash
# Generate Better Auth schema
npm run db:generate

# Push schema to database
npm run db:push

# Seed database with default data
npm run db:seed

# Reset database and reseed
npm run db:reset

# Open Prisma Studio
npm run db:studio
```

### Development
```bash
# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm run start
```

## 🔧 Configuration

### Environment Variables
Make sure your `.env` file contains:
```env
DATABASE_URL="postgresql://postgres@localhost:5432/umrahservice_sop?schema=public"
```

### Better Auth Configuration
The auth configuration is located in `lib/auth.ts` and includes:
- Prisma adapter for PostgreSQL
- Email/password authentication
- Role-based access control
- Admin plugin for user management

### Database Schema
The database includes the following tables:
- `user` - User accounts with roles
- `account` - Authentication accounts (credentials, social)
- `session` - User sessions
- `verification` - Email verification tokens
- `role` - Available roles
- `user_role` - User-role relationships

## 🔐 Authentication Flow

1. **Sign In**: Users can sign in at `/sign-in` using email/password
2. **Session Management**: Better Auth handles session creation and validation
3. **Role Checking**: User roles are stored in the database and can be checked
4. **Middleware Protection**: Routes are protected by middleware in `middleware.ts`

## 🎯 Usage Examples

### Client-Side Authentication
```typescript
import { useSession, signIn, signOut } from '@/lib/auth-client';

// Get current session
const { data: session, isLoading } = useSession();

// Sign in
await signIn.email({
  email: '<EMAIL>',
  password: 'admin123'
});

// Sign out
await signOut();
```

### Server-Side Authentication
```typescript
import { auth } from '@/lib/auth';

// Get session in API route
const session = await auth.api.getSession({
  headers: request.headers
});

// Check user role
if (session?.user?.role === 'admin') {
  // Admin-only logic
}
```

### Admin Operations
```typescript
import { authClient } from '@/lib/auth-client';

// Create new user (admin only)
const newUser = await authClient.admin.createUser({
  name: "New User",
  email: "<EMAIL>",
  password: "password123",
  role: "user"
});

// List users with filtering
const users = await authClient.admin.listUsers({
  query: {
    limit: 10,
    sortBy: "createdAt",
    sortDirection: "desc"
  }
});
```

## 🔄 Database Seeding

The seeder (`prisma/seed.ts`) automatically:
1. Creates default roles (admin, manager, user)
2. Creates default user accounts with proper password hashing
3. Assigns roles to users
4. Handles duplicate prevention (safe to run multiple times)

To run the seeder:
```bash
npm run db:seed
```

## 🚦 Next Steps

1. **Change Default Passwords**: Update the default passwords in production
2. **Configure Email Verification**: Set `requireEmailVerification: true` for production
3. **Add Social Providers**: Configure OAuth providers if needed
4. **Implement Role Permissions**: Add fine-grained permissions based on roles
5. **Add User Profile Management**: Create user profile pages and settings

## 📚 Documentation

- [Better Auth Documentation](https://better-auth.com)
- [Prisma Documentation](https://prisma.io/docs)
- [Next.js Authentication](https://nextjs.org/docs/authentication)

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check your `DATABASE_URL` in `.env`
   - Ensure PostgreSQL is running
   - Run `npm run db:push` to sync schema

2. **Authentication Not Working**
   - Check if the database is seeded: `npm run db:seed`
   - Verify the auth API route is working: `/api/auth`
   - Check browser console for errors

3. **Role Permissions**
   - Ensure users have proper roles assigned
   - Check the `user_role` table in the database
   - Verify role-checking logic in your components

### Reset Everything
If you need to start fresh:
```bash
npm run db:reset
```

This will reset the database and reseed with default data.
